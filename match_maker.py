import streamlit as st
import chromadb
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
import ollama
import os
import requests
import hashlib
import sqlite3
from datetime import datetime
import PyPDF2
from docx import Document
import io
from PIL import Image
import uuid

# Configuration
OLLAMA_HOST = os.environ.get('OLLAMA_HOST', 'http://localhost:11434')
ollama.host = OLLAMA_HOST

# Database setup
def init_database():
    """Initialize SQLite database for user management"""
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # Users table with credits
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT,
            age INTEGER,
            location TEXT,
            gender TEXT NOT NULL,
            credits INTEGER DEFAULT 100,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Profiles table with contact details
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS profiles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            description TEXT,
            partner_preference TEXT,
            photo_path TEXT,
            social_media TEXT,
            contact_email TEXT,
            contact_phone TEXT,
            contact_address TEXT,
            chroma_id TEXT UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()

# Authentication functions
def hash_password(password):
    """Hash password using SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_user(username, email, password, full_name, age, location, gender):
    """Create new user account"""
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    try:
        password_hash = hash_password(password)
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, full_name, age, location, gender, credits)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (username, email, password_hash, full_name, age, location, gender, 100))
        conn.commit()
        return True, "Account created successfully!"
    except sqlite3.IntegrityError:
        return False, "Username or email already exists!"
    finally:
        conn.close()

def authenticate_user(username, password):
    """Authenticate user login"""
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    password_hash = hash_password(password)
    cursor.execute('''
        SELECT id, username, email, full_name, age, location, gender, credits
        FROM users 
        WHERE username = ? AND password_hash = ?
    ''', (username, password_hash))
    
    user = cursor.fetchone()
    conn.close()
    
    if user:
        return True, {
            'id': user[0],
            'username': user[1],
            'email': user[2],
            'full_name': user[3],
            'age': user[4],
            'location': user[5],
            'gender': user[6],
            'credits': user[7]
        }
    return False, None

def deduct_credits(user_id, amount):
    """Deduct credits from user account"""
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT credits FROM users WHERE id = ?', (user_id,))
    current_credits = cursor.fetchone()[0]
    
    if current_credits >= amount:
        cursor.execute('UPDATE users SET credits = credits - ? WHERE id = ?', (amount, user_id))
        conn.commit()
        conn.close()
        return True, current_credits - amount
    else:
        conn.close()
        return False, "Insufficient credits!"

# ChromaDB setup
@st.cache_resource
def get_chroma_collection():
    """Initialize ChromaDB collection"""
    chroma_client = chromadb.PersistentClient(path="./chroma_db")
    collection = chroma_client.get_or_create_collection(
        name="marriage_profiles",
        embedding_function=SentenceTransformerEmbeddingFunction(
            model_name="nomic-ai/nomic-embed-text-v1.5",
            trust_remote_code=True
        )
    )
    return collection

# File processing functions
def extract_text_from_pdf(file):
    """Extract text from PDF file"""
    pdf_reader = PyPDF2.PdfReader(io.BytesIO(file.read()))
    text = ""
    for page in pdf_reader.pages:
        text += page.extract_text()
    return text

def extract_text_from_docx(file):
    """Extract text from DOCX file"""
    doc = Document(io.BytesIO(file.read()))
    text = ""
    for paragraph in doc.paragraphs:
        text += paragraph.text + "\n"
    return text

def extract_text_from_txt(file):
    """Extract text from TXT file"""
    return file.read().decode('utf-8')

def process_uploaded_file(file):
    """Process uploaded biodata file"""
    if file.type == "application/pdf":
        return extract_text_from_pdf(file)
    elif file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        return extract_text_from_docx(file)
    elif file.type == "text/plain":
        return extract_text_from_txt(file)
    else:
        st.error("Unsupported file format!")
        return None

# LLM functions
def optimize_description_with_llm(description):
    """Optimize description using LLM for better embedding"""
    prompt = f"""
    Please optimize the following biodata description for a marriage matchmaking platform. 
    Fix grammar, improve clarity, and make it more professional while maintaining all important information: Just generate the refined description without any additional information or explanations i.e. what did you modify.

    Original Description:
    {description}

    Please provide an optimized version that is:
    1. Grammatically correct
    2. Well-structured
    3. Professional yet personal
    4. Optimized for similarity matching
    5. Maintains all key information about the person
    """

    try:
        response = requests.post(
            f"{OLLAMA_HOST}/api/generate",
            json={
                "model": "llama3.2",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.95,
                }
            }
        )
        return response.json()["response"]
    except Exception as e:
        st.error(f"Error optimizing description: {str(e)}")
        return description

def find_best_matches(query, partner_preference, user_gender):
    """Find best matches using RAG, ensuring opposite gender"""
    collection = get_chroma_collection()
    
    # Combine user description and partner preference for better matching
    combined_query = f"Looking for: {partner_preference}. My profile: {query}"
    
    try:
        results = collection.query(
            query_texts=[combined_query], 
            n_results=10,  # Get more results to filter
            include=['documents', 'metadatas']
        )
        
        if not results['documents'][0]:
            return "No matches found.", {}, {}
        
        # Filter for opposite gender
        opposite_gender = 'Female' if user_gender == 'Male' else 'Male'
        filtered_matches = []
        filtered_metadatas = []
        
        for doc, metadata in zip(results['documents'][0], results['metadatas'][0]):
            cursor = sqlite3.connect('users.db').cursor()
            cursor.execute('SELECT gender FROM users WHERE id = ?', (metadata['user_id'],))
            gender = cursor.fetchone()
            cursor.close()
            if gender and gender[0] == opposite_gender:
                filtered_matches.append(doc)
                filtered_metadatas.append(metadata)
        
        if not filtered_matches:
            return "No matches found for the opposite gender.", {}, {}
        
        # Prepare context for LLM
        matches_context = ""
        profile_photos = {}
        profile_contacts = {}
        profile_names = {}
        
        for i, (doc, metadata) in enumerate(zip(filtered_matches, filtered_metadatas)):
            matches_context += f"\nMatch {i+1} (User ID: {metadata['user_id']}):\n{doc}\n"
            cursor = sqlite3.connect('users.db').cursor()
            cursor.execute('''
                SELECT p.photo_path, p.contact_email, p.contact_phone, p.contact_address, u.full_name 
                FROM profiles p 
                JOIN users u ON p.user_id = u.id 
                WHERE p.user_id = ?
            ''', (metadata['user_id'],))
            profile_data = cursor.fetchone()
            cursor.close()
            if profile_data:
                profile_photos[f"Match {i+1}"] = profile_data[0]
                profile_contacts[f"Match {i+1}"] = {
                    'email': profile_data[1],
                    'phone': profile_data[2],
                    'address': profile_data[3]
                }
                profile_names[f"Match {i+1}"] = profile_data[4] if profile_data[4] else f"User {metadata['user_id']}"
        
        prompt = f"""
        Based on the user's profile and partner preferences, analyze the provided potential matches and rank all compatible ones. Only use the profiles provided below and do not generate, assume, or include any additional profiles. All matches must be of the opposite gender ({opposite_gender}). If no profiles meet the compatibility criteria, return an empty ranking with a note indicating no suitable matches.

        User's Profile: {query}
        Partner Preferences: {partner_preference}

        Potential Matches:
        {matches_context}

        Please:
        1. Analyze compatibility factors (age, location, lifestyle, values, etc.)
        2. Rank all compatible matches in order of suitability
        3. Provide a brief explanation for each match's compatibility
        4. Include specific compatibility points for each recommendation
inoic5. If no matches are suitable, state "No suitable matches found" without ranking or explaining hypothetical matches
        6. Do not force a fixed number of matches (e.g., top 3); only include actual compatible profiles
        7. Use the full name of the user (available in metadata) in the response for each match, or use "User [ID]" if full_name is unavailable

        Format your response as follows:
        ## Ranked Matches
        ### Match X: [Full Name or User ID]
        **Compatibility Explanation:**
        - [Point 1]
        - [Point 2]
        - ...

        If no matches are found:
        No more matches found.
        """

        response = requests.post(
            f"{OLLAMA_HOST}/api/generate",
            json={
                "model": "llama3.2",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.95,
                }
            }
        )
        
        llm_response = response.json()["response"]
        
        return llm_response, profile_photos, profile_contacts
        
    except Exception as e:
        return f"Error finding matches: {str(e)}", {}, {}

# Profile management functions
def save_profile(user_id, description, partner_preference, photo_path, social_media, contact_email, contact_phone, contact_address):
    """Save or update user profile"""
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # Check if profile exists
    cursor.execute('SELECT id, chroma_id FROM profiles WHERE user_id = ?', (user_id,))
    existing_profile = cursor.fetchone()
    
    chroma_id = str(uuid.uuid4()) if not existing_profile else existing_profile[1]
    profile_link = f"https://matchmaking-app.com/profile/{user_id}"
    
    # Save to ChromaDB
    collection = get_chroma_collection()
    metadata = {
        'user_id': str(user_id),
        'profile_link': profile_link,
        'created_at': datetime.now().isoformat()
    }
    
    try:
        if existing_profile:
            # Update existing profile
            collection.delete(ids=[existing_profile[1]])
            cursor.execute('''
                UPDATE profiles 
                SET description = ?, partner_preference = ?, photo_path = ?, 
                    social_media = ?, contact_email = ?, contact_phone = ?, 
                    contact_address = ?, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ?
            ''', (description, partner_preference, photo_path, social_media, 
                  contact_email, contact_phone, contact_address, user_id))
        else:
            # Create new profile
            cursor.execute('''
                INSERT INTO profiles (user_id, description, partner_preference, photo_path, 
                                    social_media, contact_email, contact_phone, contact_address, chroma_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, description, partner_preference, photo_path, social_media, 
                  contact_email, contact_phone, contact_address, chroma_id))
        
        # Add to ChromaDB
        collection.add(
            documents=[description],
            ids=[chroma_id],
            metadatas=[metadata]
        )
        
        conn.commit()
        return True, "Profile saved successfully!"
        
    except Exception as e:
        return False, f"Error saving profile: {str(e)}"
    finally:
        conn.close()

def get_user_profile(user_id):
    """Get user profile"""
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT description, partner_preference, photo_path, social_media, 
               contact_email, contact_phone, contact_address, chroma_id
        FROM profiles WHERE user_id = ?
    ''', (user_id,))
    
    profile = cursor.fetchone()
    conn.close()
    
    if profile:
        return {
            'description': profile[0],
            'partner_preference': profile[1],
            'photo_path': profile[2],
            'social_media': profile[3],
            'contact_email': profile[4],
            'contact_phone': profile[5],
            'contact_address': profile[6],
            'chroma_id': profile[7]
        }
    return None

def delete_profile(user_id):
    """Delete user profile"""
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # Get chroma_id first
    cursor.execute('SELECT chroma_id FROM profiles WHERE user_id = ?', (user_id,))
    result = cursor.fetchone()
    
    if result:
        chroma_id = result[0]
        
        # Delete from ChromaDB
        collection = get_chroma_collection()
        try:
            collection.delete(ids=[chroma_id])
        except:
            pass  # Handle case where document doesn't exist in ChromaDB
        
        # Delete from SQLite
        cursor.execute('DELETE FROM profiles WHERE user_id = ?', (user_id,))
        conn.commit()
    
    conn.close()

# Image handling
def save_uploaded_image(uploaded_file, user_id):
    """Save uploaded image and return path"""
    if uploaded_file is not None:
        # Create images directory if it doesn't exist
        os.makedirs("images", exist_ok=True)
        
        # Generate unique filename
        file_extension = uploaded_file.name.split('.')[-1]
        filename = f"user_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
        filepath = os.path.join("images", filename)
        
        # Save file
        with open(filepath, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        return filepath
    return None

# Streamlit App
def main():
    st.set_page_config(
        page_title="AI Matchmaking",
        page_icon="🧑🏾‍🤝‍🧑🏽",
        layout="centered"
    )
    
    # Initialize database
    init_database()
    
    # Session state initialization
    if 'logged_in' not in st.session_state:
        st.session_state.logged_in = False
    if 'user' not in st.session_state:
        st.session_state.user = None
    if 'show_contacts' not in st.session_state:
        st.session_state.show_contacts = {}
    
    # Main header
    st.title("AI Matchmaking Platform")
    st.markdown("Find your perfect life partner with AI-powered matching")
    
    # Authentication
    if not st.session_state.logged_in:
        tab1, tab2 = st.tabs(["Login", "Sign Up"])
        
        with tab1:
            st.subheader("Login")
            with st.form("login_form"):
                username = st.text_input("Username")
                password = st.text_input("Password", type="password")
                submitted = st.form_submit_button("Login")
                
                if submitted:
                    success, user_data = authenticate_user(username, password)
                    if success:
                        st.session_state.logged_in = True
                        st.session_state.user = user_data
                        st.success("Login successful!")
                        st.rerun()
                    else:
                        st.error("Invalid username or password!")
        
        with tab2:
            st.subheader("Create Account")
            with st.form("signup_form"):
                new_username = st.text_input("Username*")
                new_email = st.text_input("Email*")
                new_password = st.text_input("Password*", type="password")
                confirm_password = st.text_input("Confirm Password*", type="password")
                full_name = st.text_input("Full Name*")
                age = st.number_input("Age*", min_value=18, max_value=100, value=25)
                location = st.text_input("Location*")
                gender = st.selectbox("Gender*", ["Male", "Female"])
                
                submitted = st.form_submit_button("Create Account")
                
                if submitted:
                    if new_password != confirm_password:
                        st.error("Passwords don't match!")
                    elif len(new_password) < 6:
                        st.error("Password must be at least 6 characters!")
                    else:
                        success, message = create_user(
                            new_username, new_email, new_password, 
                            full_name, age, location, gender
                        )
                        if success:
                            st.success(message)
                        else:
                            st.error(message)
    
    else:
        # Main application for logged-in users
        st.sidebar.title(f"Welcome, {st.session_state.user['full_name']}!")
        st.sidebar.metric("Credits", st.session_state.user['credits'])
        
        # Sidebar navigation
        menu = st.sidebar.selectbox(
            "Navigation",
            ["Profile Management", "Find Matches", "Account Settings"]
        )
        
        if st.sidebar.button("Logout"):
            st.session_state.logged_in = False
            st.session_state.user = None
            st.session_state.show_contacts = {}
            st.rerun()
        
        # Profile Management
        if menu == "Profile Management":
            st.header("Profile Management")
            
            # Get existing profile
            current_profile = get_user_profile(st.session_state.user['id'])
            
            # Profile tabs
            tab1, tab2, tab3 = st.tabs(["Upload Biodata", "Edit Profile", "View Profile"])
            
            with tab1:
                st.subheader("Upload Biodata Document")
                uploaded_file = st.file_uploader(
                    "Choose a file", 
                    type=['pdf', 'docx', 'txt'],
                    help="Upload your biodata in PDF, DOCX, or TXT format"
                )
                
                if uploaded_file is not None:
                    with st.spinner("Processing file..."):
                        extracted_text = process_uploaded_file(uploaded_file)
                        
                        if extracted_text:
                            st.text_area("Extracted Text", extracted_text, height=200)
                            
                            if st.button("Optimize with AI"):
                                with st.spinner("Optimizing description..."):
                                    optimized_text = optimize_description_with_llm(extracted_text)
                                    st.session_state.optimized_description = optimized_text
                                    st.success("Description optimized!")
                                    st.text_area("Optimized Description", optimized_text, height=200)
            
            with tab2:
                st.subheader("Edit Profile Details")
                
                with st.form("profile_form"):
                    # Description
                    description = st.text_area(
                        "Description*",
                        value=current_profile['description'] if current_profile else 
                              st.session_state.get('optimized_description', ''),
                        height=200,
                        help="Describe yourself, your background, interests, and what makes you unique"
                    )
                    
                    # Partner preferences
                    partner_preference = st.text_area(
                        "Partner Preferences*",
                        value=current_profile['partner_preference'] if current_profile else '',
                        height=150,
                        help="Describe your ideal partner - age range, profession, values, interests, etc."
                    )
                    
                    # Photo upload
                    st.subheader("Photo")
                    uploaded_photo = st.file_uploader(
                        "Upload your photo",
                        type=['jpg', 'jpeg', 'png'],
                        help="Upload a clear, recent photo of yourself"
                    )
                    
                    # Contact details
                    st.subheader("Contact Information")
                    contact_email = st.text_input(
                        "Contact Email",
                        value=current_profile['contact_email'] if current_profile else '',
                        help="Your contact email (optional)"
                    )
                    contact_phone = st.text_input(
                        "Contact Phone",
                        value=current_profile['contact_phone'] if current_profile else '',
                        help="Your contact phone number (optional)"
                    )
                    contact_address = st.text_area(
                        "Contact Address",
                        value=current_profile['contact_address'] if current_profile else '',
                        help="Your address (optional)"
                    )
                    
                    # Social media
                    social_media = st.text_area(
                        "Social Media & Contact (Optional)",
                        value=current_profile['social_media'] if current_profile else '',
                        help="LinkedIn, Instagram, or other professional social media profiles"
                    )
                    
                    submitted = st.form_submit_button("Save Profile")
                    
                    if submitted:
                        if description and partner_preference:
                            # Save photo if uploaded
                            photo_path = None
                            if uploaded_photo:
                                photo_path = save_uploaded_image(uploaded_photo, st.session_state.user['id'])
                            elif current_profile and current_profile['photo_path']:
                                photo_path = current_profile['photo_path']
                            
                            success, message = save_profile(
                                st.session_state.user['id'],
                                description,
                                partner_preference,
                                photo_path,
                                social_media,
                                contact_email,
                                contact_phone,
                                contact_address
                            )
                            
                            if success:
                                st.success(message)
                            else:
                                st.error(message)
                        else:
                            st.error("Please fill in all required fields!")
            
            with tab3:
                st.subheader("Your Profile")
                
                if current_profile:
                    # Display photo
                    if current_profile['photo_path'] and os.path.exists(current_profile['photo_path']):
                        image = Image.open(current_profile['photo_path'])
                        st.image(image, width=200, caption="Your Photo")
                    
                    st.write("**Description:**")
                    st.write(current_profile['description'])
                    
                    st.write("**Partner Preferences:**")
                    st.write(current_profile['partner_preference'])
                    
                    if current_profile['contact_email']:
                        st.write("**Contact Email:**")
                        st.write(current_profile['contact_email'])
                    
                    if current_profile['contact_phone']:
                        st.write("**Contact Phone:**")
                        st.write(current_profile['contact_phone'])
                    
                    if current_profile['contact_address']:
                        st.write("**Contact Address:**")
                        st.write(current_profile['contact_address'])
                    
                    if current_profile['social_media']:
                        st.write("**Social Media:**")
                        st.write(current_profile['social_media'])
                    
                    if st.button("Delete Profile", type="secondary"):
                        if st.button("Confirm Delete", type="secondary"):
                            delete_profile(st.session_state.user['id'])
                            st.success("Profile deleted successfully!")
                            st.rerun()
                else:
                    st.info("No profile found. Please create your profile first!")
        
        # Find Matches
        elif menu == "Find Matches":
            st.header("Find Your Perfect Match")
            
            current_profile = get_user_profile(st.session_state.user['id'])
            
            if current_profile:
                st.subheader("Your Profile Summary")
                st.write(current_profile['description'][:200] + "..." if len(current_profile['description']) > 200 else current_profile['description'])
                
                if st.button("Find Matches", type="primary"):
                    with st.spinner("Finding your perfect matches..."):
                        matches, profile_photos, profile_contacts = find_best_matches(
                            current_profile['description'],
                            current_profile['partner_preference'],
                            st.session_state.user['gender']
                        )
                        
                        st.subheader("Your Matches")
                        st.markdown(matches)
                        
                        # Display photos and contact details
                        for match, photo_path in profile_photos.items():
                            if photo_path and os.path.exists(photo_path):
                                st.write(f"**{match} Photo:**")
                                image = Image.open(photo_path)
                                st.image(image, width=200, caption=f"{match} Photo")
                            
                            if match in profile_contacts:
                                contact_info = profile_contacts[match]
                                contact_key = f"{match}_contact_{uuid.uuid4()}"  # Unique key for each button
                                
                                if st.button(f"View {match} Contact Details (10 credits)", key=contact_key):
                                    if st.session_state.user['credits'] >= 10:
                                        success, new_credits = deduct_credits(st.session_state.user['id'], 10)
                                        if success:
                                            st.session_state.show_contacts[match] = True
                                            st.session_state.user['credits'] = new_credits
                                            st.success(f"Contact details unlocked! Remaining credits: {new_credits}")
                                            st.rerun()
                                        else:
                                            st.error("Error deducting credits. Please try again.")
                                    else:
                                        st.error("Insufficient credits! Please purchase more credits to view contact details.")
                                        if st.button("Purchase Credits", key=f"purchase_{match}_{uuid.uuid4()}"):
                                            st.write("Redirecting to payment page... (Implement payment gateway here)")
                                
                                if st.session_state.show_contacts.get(match, False):
                                    st.write(f"**{match} Contact Details:**")
                                    if contact_info['email']:
                                        st.write(f"Email: {contact_info['email']}")
                                    if contact_info['phone']:
                                        st.write(f"Phone: {contact_info['phone']}")
                                    if contact_info['address']:
                                        st.write(f"Address: {contact_info['address']}")
                
                # Additional matching options
                st.subheader("Refine Your Search")
                with st.expander("Advanced Search Options"):
                    age_range = st.slider("Preferred Age Range", 18, 60, (25, 35))
                    location_preference = st.text_input("Preferred Location")
                    additional_criteria = st.text_area("Additional Criteria")
                    
                    if st.button("Search with Filters"):
                        # Enhanced search with filters
                        enhanced_preference = f"""
                        {current_profile['partner_preference']}
                        Age range: {age_range[0]} to {age_range[1]} years
                        Location preference: {location_preference}
                        Additional criteria: {additional_criteria}
                        """
                        
                        with st.spinner("Searching with your preferences..."):
                            matches, profile_photos, profile_contacts = find_best_matches(
                                current_profile['description'],
                                enhanced_preference,
                                st.session_state.user['gender']
                            )
                            
                            st.subheader("Filtered Matches")
                            st.markdown(matches)
                            
                            # Display photos and contact details
                            for match, photo_path in profile_photos.items():
                                if photo_path and os.path.exists(photo_path):
                                    st.write(f"**{match} Photo:**")
                                    image = Image.open(photo_path)
                                    st.image(image, width=200, caption=f"{match} Photo")
                                
                                if match in profile_contacts:
                                    contact_info = profile_contacts[match]
                                    contact_key = f"{match}_contact_filtered_{uuid.uuid4()}"
                                    
                                    if st.click(f"View {match} Contact Details (10 credits)", key=contact_key):
                                        if st.session_state.user['credits'] >= 10:
                                            success, new_credits = deduct_credits(st.session_state.user['id'], 10)
                                            if success:
                                                st.session_state.show_contacts[match] = True
                                                st.session_state.user['credits'] = new_credits
                                                st.success(f"Contact details unlocked! Remaining credits: {new_credits}")
                                                st.rerun()
                                            else:
                                                st.error("Error deducting credits. Please try again.")
                                        else:
                                            st.error("Insufficient credits! Please purchase more credits to view contact details.")
                                            if st.button("Purchase Credits", key=f"purchase_filtered_{match}_{uuid.uuid4()}"):
                                                st.write("Redirecting to payment page... (Implement payment gateway here)")
                                    
                                    if st.session_state.show_contacts.get(match, False):
                                        st.write(f"**{match} Contact Details:**")
                                        if contact_info['email']:
                                            st.write(f"Email: {contact_info['email']}")
                                        if contact_info['phone']:
                                            st.write(f"Phone: {contact_info['phone']}")
                                        if contact_info['address']:
                                            st.write(f"Address: {contact_info['address']}")
            else:
                st.warning("Please create your profile first to find matches!")
                if st.button("Go to Profile Management"):
                    st.session_state.menu = "Profile Management"
                    st.rerun()
        
        # Account Settings
        elif menu == "Account Settings":
            st.header("Account Settings")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Your Information")
                st.write(f"**Username:** {st.session_state.user['username']}")
                st.write(f"**Email:** {st.session_state.user['email']}")
                st.write(f"**Full Name:** {st.session_state.user['full_name']}")
                st.write(f"**Age:** {st.session_state.user['age']}")
                st.write(f"**Location:** {st.session_state.user['location']}")
                st.write(f"**Gender:** {st.session_state.user['gender']}")
                st.write(f"**Credits:** {st.session_state.user['credits']}")
            
            with col2:
                st.subheader("App Statistics")
                collection = get_chroma_collection()
                total_profiles = collection.count()
                st.metric("Total Profiles in Database", total_profiles)
                
                # Show profile status
                current_profile = get_user_profile(st.session_state.user['id'])
                if current_profile:
                    st.success("✅ Profile Active")
                else:
                    st.warning("❌ No Profile Created")
                
                if st.button("Purchase Credits"):
                    st.write("Redirecting to payment page... (Implement payment gateway here)")

if __name__ == "__main__":
    main()