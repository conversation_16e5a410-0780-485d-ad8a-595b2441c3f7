import chromadb
import ollama
import os
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction

OLLAMA_HOST = os.environ.get('OLLAMA_HOST', 'http://app:11434')
ollama.host = OLLAMA_HOST

client = chromadb.PersistentClient(path="./chroma_")
collection = client.get_collection(name="my_vectors")  



query = "lived in paris"

results = collection.query(query_texts=[query], n_results=1)
result = results['documents'][0]

print(result)
